@page "/Sell/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]
<MudDialog Style="min-width: 100vw; height: 150vh;">
    <TitleContent>
        <div class="d-flex align-center gap-3">
            <MudIcon Icon="@Icons.Material.Filled.Receipt" Color="Color.Primary" Size="Size.Large" />
            <div>
                <MudText Typo="Typo.h5" Color="Color.Primary">@(id == Guid.Empty ? "فاتورة بيع جديدة" : "تعديل فاتورة بيع")</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">@DateTime.Now.ToString("dd-MM-yyyy")</MudText>
            </div>
        </div>
    </TitleContent>

    <DialogContent>
        <EditForm Model="@_Sell" @onkeydown="keydownForm" OnValidSubmit="Upsert">
            <DataAnnotationsValidator />

            <!-- تصميم العمودين الرئيسي -->
            <div class="two-column-layout">

                <!-- العمود الأيمن: حقول النماذج وعناصر التحكم -->
                <div class="right-column compact-right-column">

                    <!-- قسم معلومات الفاتورة الأساسية -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">معلومات الفاتورة</MudText>
                        <MudGrid Spacing="1">
                            <MudItem xs="12" sm="6">
                                <MudDatePicker @bind-Date="_dateSell"
                                               Label="التاريخ"
                                               Variant="Variant.Outlined"
                                               AdornmentIcon="@Icons.Material.Filled.Event"
                                               Adornment="Adornment.End"
                                               Margin="Margin.Dense"
                                               Class="compact-field" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <div class="d-flex align-center gap-1 invoice-navigation">
                                    <!-- زر التالي (→) -->
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowForward"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   Variant="Variant.Outlined"
                                                   OnClick="NavigateToNextInvoice"
                                                   Title="الفاتورة التالية (→)"
                                                   Class="navigation-btn flex-shrink-0"
                                                   Disabled="@(_isLoading || !SellsNum.Any() || (id != Guid.Empty && SellsNum.FindIndex(x => x.Id == id) >= SellsNum.Count - 1))" />

                                    <!-- حقل رقم الفاتورة -->
                                    <MudTextField T="int" Value="_Sell.InvoiceNo" @ref="InvoiceNo"
                                                  Label="رقم الفاتورة" OnKeyDown="KeyDownInvoice"
                                                  ValueChanged="@((int value) => { _Sell.InvoiceNo = value; ValidateInvoiceNumber(); })"
                                                  Variant="Variant.Outlined"
                                                  AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                  Adornment="Adornment.End"
                                                  For="@(() => _Sell.InvoiceNo)"
                                                  Class="invoice-field flex-grow-1 compact-field"
                                                  Margin="Margin.Dense" />

                                    <!-- زر السابق (←) -->
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   Variant="Variant.Outlined"
                                                   OnClick="NavigateToPreviousInvoice"
                                                   Title="الفاتورة السابقة (←)"
                                                   Class="navigation-btn flex-shrink-0"
                                                   Disabled="@(_isLoading || !SellsNum.Any() || (id != Guid.Empty && SellsNum.FindIndex(x => x.Id == id) <= 0))" />
                                </div>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>

                    <!-- قسم المخزن والعميل -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">المخزن والعميل</MudText>
                        <MudGrid Spacing="1">
                            <MudItem xs="12" sm="6">
                                <MudSelect T="Guid?" @bind-Value="_Sell.StoreId" Label="المخازن"
                                           Variant="Variant.Outlined"
                                           Adornment="Adornment.End"
                                           Disabled="_Sell.Id!=Guid.Empty" AdornmentIcon="@Icons.Material.Filled.Store" Required="true"
                                           RequiredError="يجب اختيار المخزن" For="@(() =>_Sell.StoreId)"
                                           Margin="Margin.Dense"
                                           Class="compact-field">
                                    <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                                    @foreach (var store in stores)
                                    {
                                        <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <div class="d-flex gap-1">
                                    <MudComboBox @bind-Value="_Sell.Client"
                                                 Label="العميل" Editable="true"
                                                 SearchFunc="@SearchClientFunc"
                                                 ToStringFunc="@(e => e?.Name ?? string.Empty)"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.Person"
                                                 Adornment="Adornment.End" For="@(() => _Sell.Client)"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field">
                                        @foreach (var client in clients)
                                        {
                                            <MudComboBoxItem Value="@client" Text="@client.Name">@client.Name</MudComboBoxItem>
                                        }
                                    </MudComboBox>

                                    <MudFab Style="height:32px;width:32px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.Person3"
                                            OnClick="AddNewClient" Color="Color.Info"
                                            Size="Size.Small" />
                                </div>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>

                    <!-- قسم إضافة الأصناف -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">إضافة صنف</MudText>

                        <!-- البحث عن الصنف -->
                        <MudGrid Spacing="1">
                            <MudItem xs="12">
                                <div class="d-flex gap-1">
                                    <MudComboBox T="StoreItemForSellDTO"
                                                 Editable="true"
                                                 Value="selectedStoreItem?.StoreItemExp?.StoreItem" @ref="ItemForAdd"
                                                 ValueChanged="ChangeItem"
                                                 Label="الصنف"
                                                 OpenMenuAfterClear="true"
                                                 Immediate="true"
                                                 SearchFunc="@SearchItemFunc" ToStringFunc="@(e=> e==null?null : $"{e.Item.Name}")"
                                                 OnKeyDown="SelectItem"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.Inventory"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field">
                                        @foreach (var item in items.Select(x => new StoreItemForSellDTO() { Item = new ItemCMDTO() { Id = x.Item.Id, Name = x.Item.Name }, StoreId = x.StoreId }))
                                        {
                                            <MudComboBoxItem Value="@item" Text="@item.Item.Name">@item.Item.Name</MudComboBoxItem>
                                        }
                                    </MudComboBox>

                                    <MudFab Style="height:32px;width:32px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.AddShoppingCart"
                                            OnClick="AddSellItem" Color="Color.Info"
                                            Size="Size.Small" />
                                </div>
                            </MudItem>

                            <!-- تفاصيل الصنف -->
                            <MudItem xs="6" sm="4">
                                <MudSelect T="Guid" Variant="Variant.Outlined"
                                           Value="selectedStoreItem?.StoreItemExp?.Id ?? Guid.Empty"
                                           ValueChanged="ChangeExp"
                                           Label="تاريخ الصلاحية"
                                           AdornmentIcon="@Icons.Material.Filled.Event"
                                           Adornment="Adornment.End"
                                           Margin="Margin.Dense"
                                           Class="compact-field"
                                           ToStringFunc="@(expId => GetExpDisplayText(expId))">
                                    <MudSelectItem T="Guid" Value="Guid.Empty">اختر صلاحية</MudSelectItem>
                                    @if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null)
                                    {
                                        var selectedItem = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
                                        if (selectedItem is not null)
                                        {
                                            if (!selectedItem.Item.IsHaveExp)
                                            {
                                                if (selectedItem.StoreItemExps.Any())
                                                {
                                                    <MudSelectItem T="Guid" Value="selectedItem.StoreItemExps.First().Id"> @(selectedItem.StoreItemExps.First().Exp) </MudSelectItem>
                                                }
                                            }
                                            @foreach (var exp in selectedItem.StoreItemExps)
                                            {
                                                <MudSelectItem T="Guid" Value="exp.Id">@exp.Exp</MudSelectItem>
                                            }
                                        }
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudSelect T="ItemUnitDTO"
                                           Value="selectedStoreItem.ItemUnit"
                                           ValueChanged="ChangeItemUnit"
                                           Label="الوحدة"
                                           Variant="Variant.Outlined"
                                           AdornmentIcon="@Icons.Material.Filled.Scale"
                                           Adornment="Adornment.End"
                                           For="@(() =>selectedStoreItem.ItemUnit)"
                                           Margin="Margin.Dense"
                                           Class="compact-field">
                                    <MudSelectItem T="ItemUnitDTO" Value="null">اختر وحدة</MudSelectItem>
                                    @if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null)
                                    {
                                        var selectedItemForUnits = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
                                        if (selectedItemForUnits is not null)
                                        {
                                            @foreach (var unit in selectedItemForUnits.Item.ItemUnits)
                                            {
                                                <MudSelectItem T="ItemUnitDTO" Value="unit"> @unit.Unit.Name </MudSelectItem>
                                            }
                                        }
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudTextField T="string"
                                              Value="@AvailableQuantityDisplay"
                                              ReadOnly="true"
                                              Variant="Variant.Outlined"
                                              Label="الكمية المتاحة"
                                              AdornmentIcon="@Icons.Material.Filled.Inventory"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field"
                                              Style="@(AvailableQuantityDisplay == "غير متاح" ? "color: #999;" : AvailableQuantityDisplay.Contains("خطأ") ? "color: #f44336;" : "color: #4caf50; font-weight: 500;")" />
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudNumericField @bind-Value="selectedStoreItem.Quantity"
                                                 Label="الكمية"
                                                 Variant="Variant.Outlined"
                                                 Min="0"
                                                 Disabled="selectedStoreItem?.StoreItemExp==null"
                                                 AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudNumericField @bind-Value="selectedStoreItem.SalePrice" Min="0"
                                                 Label="سعر البيع"
                                                 Variant="Variant.Outlined"
                                                 Disabled="selectedStoreItem?.StoreItemExp==null"
                                                 AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="12" sm="4" Class="d-flex align-center">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           OnClick="AddSellItem"
                                           StartIcon="@Icons.Material.Filled.Add"
                                           FullWidth="true"
                                           Size="Size.Small">
                                    إضافة الصنف
                                </MudButton>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>


                    <!-- قسم الإجماليات وطرق الدفع -->
                    <MudPaper Elevation="1" Class="pa-2 mb-2">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">الإجماليات والدفع</MudText>
                        <MudGrid Spacing="1">
                            @if (id == Guid.Empty)
                            {
                                <MudItem xs="12" sm="6">
                                    <MudSelect T="Guid?"
                                               @bind-Value="_Sell.TreasuryId"
                                               Label="الخزينة" For="@(() =>_Sell.TreasuryId)"
                                               Variant="Variant.Outlined"
                                               Margin="Margin.Dense"
                                               Class="compact-field">
                                        <MudSelectItem T="Guid?" Value="null">اختيار الخزينة</MudSelectItem>
                                        @foreach (var userTreasury in userTreasuries)
                                        {
                                            <MudSelectItem T="Guid?" Value="userTreasury.Id">@userTreasury.Treasury.Name</MudSelectItem>
                                        }
                                    </MudSelect>
                                </MudItem>
                            }

                            <MudItem xs="6" sm="3">
                                <MudTextField @bind-Value="_Sell.Total"
                                              Label="الإجمالي"
                                              Variant="Variant.Outlined"
                                              ReadOnly="true"
                                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudTextField T="decimal" Value="_Sell.DiscountValue" ValueChanged="ChangeDiscountValue"
                                              Label="التخفيض"
                                              Variant="Variant.Outlined"
                                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudTextField @bind-Value="_Sell.FinalTotal"
                                              Label="الإجمالي بعد التخفيض" ReadOnly="true"
                                              Variant="Variant.Outlined"
                                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudNumericField @bind-Value="_Sell.Paid"
                                                 Label="المدفوع" Disabled="@(id != Guid.Empty)"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.Payment"
                                                 Adornment="Adornment.End"
                                                 Margin="Margin.Dense"
                                                 Class="compact-field" />
                            </MudItem>

                            <MudItem xs="6" sm="3">
                                <MudTextField Value="@(_Sell.FinalTotal - _Sell.Paid)"
                                              Label="المتبقي"
                                              Variant="Variant.Outlined"
                                              ReadOnly="true"
                                              AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                                              Adornment="Adornment.End"
                                              Margin="Margin.Dense"
                                              Class="compact-field" />
                            </MudItem>

                            <MudItem xs="3" sm="3">
                                <div class="d-flex align-center small-radio-group">
                                    <MudRadioGroup T="bool" Value="_Sell.IsDiscountValue" ValueChanged="ChangeIsDiscount" Class="d-flex gap-1">
                                        <MudRadio Value="true" Color="Color.Success">
                                            <div class="d-flex gap-1 align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.EventAvailable" Size="Size.Small" />
                                                <span style="font-size: 0.65rem;">بالقيمة</span>
                                            </div>
                                        </MudRadio>
                                        <MudRadio Value="false" Color="Color.Error">
                                            <div class="d-flex gap-1 align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Small" />
                                                <span style="font-size: 0.65rem;">بالنسبة</span>
                                            </div>
                                        </MudRadio>
                                    </MudRadioGroup>
                                </div>
                            </MudItem>

                        </MudGrid>
                    </MudPaper>
                </div>

                <!-- العمود الأيسر: قائمة الأصناف -->
                <div class="left-column">
                    <MudPaper Elevation="0" Class="pa-2 items-list-container transparent-background">
                        <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-1">قائمة الأصناف</MudText>

                        <div class="items-table-container">

                            <MudTable Items="@_Sell.SellItemDTOs"
                                      Hover="true"
                                      Striped="true"
                                      Bordered="true"
                                      Height="calc(100vh - 300px)"
                                      FixedHeader="true"
                                      Class="compact-table">
                                <HeaderContent>
                                    <MudTh Style="width: 5%;">#</MudTh>
                                    <MudTh Style="width: 25%;">الصنف</MudTh>
                                    <MudTh Style="width: 10%;">الوحدة</MudTh>
                                    <MudTh Style="width: 10%;">الصلاحية</MudTh>
                                    <MudTh Style="width: 10%;">الكمية</MudTh>
                                    <MudTh Style="width: 10%;">السعر</MudTh>
                                    <MudTh Style="width: 10%;">بعد التخفيض</MudTh>
                                    <MudTh Style="width: 10%;">الإجمالي</MudTh>
                                    <MudTh Style="width: 10%;">إجراءات</MudTh>
                                </HeaderContent>
                                <RowTemplate Context="item">
                                    <MudTd DataLabel="#" Style="font-size: 0.8rem;">@(_Sell.SellItemDTOs.IndexOf(item) + 1)</MudTd>
                                    <MudTd DataLabel="الصنف" Style="font-size: 0.8rem;">@item.StoreItemExp.StoreItem.Item.Name</MudTd>
                                    <MudTd DataLabel="الوحدة" Style="font-size: 0.8rem;">@item.ItemUnit.Unit.Name</MudTd>
                                    <MudTd DataLabel="الصلاحية" Style="font-size: 0.8rem;">@(item.StoreItemExp.Exp is null ? "لا يوجد" : item.StoreItemExp.Exp.Value.ToShortDateString())</MudTd>
                                    <MudTd DataLabel="الكمية" Style="font-size: 0.8rem;">@item.Quantity.ToString("N2")</MudTd>
                                    <MudTd DataLabel="السعر" Style="font-size: 0.8rem;">@item.SalePrice.ToString("N2")</MudTd>
                                    <MudTd DataLabel="بعد التخفيض" Style="font-size: 0.8rem;">@item.SalePriceAfterDiscount.ToString("N2")</MudTd>
                                    <MudTd DataLabel="الإجمالي" Style="font-size: 0.8rem; font-weight: 600;">@((item.Quantity * item.SalePriceAfterDiscount).ToString("N2"))</MudTd>
                                    <MudTd DataLabel="إجراءات">
                                        <div class="d-flex gap-1">
                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                           Color="Color.Error"
                                                           Size="Size.Small"
                                                           OnClick="@(() => DeleteItem(item))"
                                                           Title="حذف" />
                                        </div>
                                    </MudTd>
                                </RowTemplate>
                            </MudTable>
                        </div>
                    </MudPaper>
                </div>
            </div>

            <!-- شريط الأزرار السفلي -->
            <div class="action-buttons-bar">
                <MudPaper Elevation="3" Class="pa-2">
                    <div class="d-flex align-center gap-2 justify-space-between">
                        <div class="d-flex gap-2">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Error"
                                       OnClick="Back"
                                       StartIcon="@Icons.Material.Filled.ArrowBack"
                                       Disabled="@_isLoading"
                                       Size="Size.Small">
                                رجوع
                            </MudButton>
                        </div>

                        <div class="d-flex gap-2">
                            @if (id != Guid.Empty)
                            {
                                <MudButton Variant="Variant.Filled" Color="Color.Primary"
                                           OnClick="@(()=>AddReceipt(FinancialId.Sale))"
                                           EndIcon="@(_isReceiptLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Receipt)"
                                           Disabled="@_isLoading"
                                           Size="Size.Small">
                                    @if (_isReceiptLoading)
                                    {
                                        <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                        <span style="margin-right: 8px;">جاري الإضافة...</span>
                                    }
                                    else
                                    {
                                        <span>اضافة ايصال</span>
                                    }
                                </MudButton>

                                <!-- زر معاينة الفاتورة -->
                                <MudButton Variant="Variant.Text"
                                           Color="Color.Info"
                                           OnClick="@PreviewSell"
                                           Disabled="@_isPrinting"
                                           StartIcon="@Icons.Material.Filled.Preview"
                                           Size="Size.Small">
                                    معاينة
                                </MudButton>
                            }

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Success"
                                       OnClick="NewSell"
                                       StartIcon="@(_isNewLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Add)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small">
                                @if (_isNewLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 8px;">جاري الإنشاء...</span>
                                }
                                else
                                {
                                    <span>جديد (F8)</span>
                                }
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="Upsert"
                                       StartIcon="@(_isSaveLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Save)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small">
                                @if (_isSaveLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 8px;">جاري الحفظ...</span>
                                }
                                else
                                {
                                    <span>حفظ (F2)</span>
                                }
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Error"
                                       OnClick="@(() => Delete(_Sell))"
                                       StartIcon="@(_isDeleteLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Delete)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small">
                                @if (_isDeleteLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 8px;">جاري الحذف...</span>
                                }
                                else
                                {
                                    <span>حذف (Del)</span>
                                }
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Surface" OnClick="@Print"
                                       StartIcon="@(_isPrintLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Print)"
                                       Disabled="@_isLoading"
                                       Size="Size.Small">
                                @if (_isPrintLoading)
                                {
                                    <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-right: 8px;">جاري الطباعة...</span>
                                }
                                else
                                {
                                    <span>طباعة</span>
                                }
                            </MudButton>
                        </div>
                    </div>
                </MudPaper>
            </div>

            <!-- رسالة التأكيد -->
            <MudMessageBox @ref="mbox"
                           CancelText="إلغاء"
                           Class="rounded-lg">
                <MessageContent>
                    <div class="d-flex flex-column gap-4">
                        <MudIcon Icon="@(_isDeleteMessage? Icons.Material.Filled.Delete:Icons.Material.Filled.Warning)"
                                 Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                                 Size="Size.Large"
                                 Class="mx-auto" />
                        <MudText Align="Align.Center">
                            @_message
                        </MudText>
                    </div>
                </MessageContent>
                <YesButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                               StartIcon="@(_isDeleteMessage? Icons.Material.Filled.DeleteForever:Icons.Material.Filled.Info)"
                               Size="Size.Large">
                        تأكيد
                    </MudButton>
                </YesButton>
                <NoButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(Color.Default)"
                               StartIcon="@(Icons.Material.Filled.Cancel)"
                               Size="Size.Large">
                        إلغاء
                    </MudButton>
                </NoButton>
            </MudMessageBox>
        </EditForm>
    </DialogContent>
</MudDialog>



<style>
    /* التصميم ذو العمودين الرئيسي */
    .two-column-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        height: calc(100vh - 200px);
        overflow: hidden;
        padding: 8px;
    }

    /* العمود الأيمن - حقول النماذج مع تصغير */
    .right-column.compact-right-column {
        display: flex;
        flex-direction: column;
        gap: 4px !important;
        overflow-y: auto;
        padding-right: 4px !important;
        max-height: 100%;
    }

    /* العمود الأيسر - قائمة الأصناف بالحجم الأصلي */
    .left-column {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        max-height: 100%;
    }

    /* حاوية قائمة الأصناف */
    .items-list-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    /* حاوية الجدول */
    .items-table-container {
        flex: 1;
        overflow: hidden;
        margin-top: 8px;
    }

    /* جدول بالحجم الأصلي */
    .compact-table {
        font-size: 0.8rem !important;
    }

    .compact-table .mud-table-cell {
        padding: 4px 8px !important;
        font-size: 0.8rem !important;
    }

    .compact-table .mud-table-head .mud-table-cell {
        padding: 6px 8px !important;
        font-size: 0.8rem !important;
        font-weight: 600 !important;
    }

    /* شريط الأزرار السفلي بالحجم الأصلي */
    .action-buttons-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: white;
        border-top: 1px solid #e0e0e0;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
    }

    /* تحسينات للتخطيط العربي RTL */
    .invoice-navigation {
        direction: rtl;
    }

    .invoice-navigation .d-flex {
        flex-direction: row-reverse;
    }

    /* تحسين أزرار التنقل */
    .navigation-btn {
        min-width: 32px;
        height: 32px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .navigation-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .navigation-btn:active {
        transform: scale(0.95);
    }

    /* تحسين حقل رقم الفاتورة */
    .invoice-field {
        margin: 0 4px;
    }

    /* تصميم متجاوب */
    @media (max-width: 1200px) {
        .two-column-layout {
            grid-template-columns: 1fr;
            grid-template-rows: auto 1fr;
            height: auto;
            max-height: calc(100vh - 200px);
        }

        .right-column {
            max-height: 50vh;
            overflow-y: auto;
        }

        .left-column {
            max-height: 50vh;
        }
    }

    @media (max-width: 768px) {
        .two-column-layout {
            gap: 8px;
            padding: 4px;
        }

        .action-buttons-bar .d-flex {
            flex-wrap: wrap;
            gap: 4px !important;
        }

        .action-buttons-bar .mud-button {
            font-size: 0.7rem !important;
            padding: 4px 8px !important;
        }
    }

    /* تحسين التمرير في العمود الأيمن */
    .right-column::-webkit-scrollbar {
        width: 6px;
    }

    .right-column::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .right-column::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .right-column::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* إخفاء شريط الأزرار من المساحة المحجوزة للمحتوى */
    .two-column-layout {
        margin-bottom: 60px;
    }

    /* إزالة الخلفية البيضاء من قائمة الأصناف */
    .transparent-background {
        background-color: transparent !important;
        background: transparent !important;
    }

    .transparent-background .mud-paper {
        background-color: transparent !important;
        background: transparent !important;
    }

    /* إزالة الخلفية من الجدول أيضاً */
    .items-list-container .mud-table {
        background-color: transparent !important;
        background: transparent !important;
    }

    .items-list-container .mud-table-container {
        background-color: transparent !important;
        background: transparent !important;
    }

    /* إزالة الخلفية من رؤوس الجدول */
    .items-list-container .mud-table-head {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    /* تحسين مظهر الصفوف بدون خلفية بيضاء */
    .items-list-container .mud-table-row {
        background-color: transparent !important;
    }

    .items-list-container .mud-table-row:hover {
        background-color: rgba(0, 0, 0, 0.04) !important;
    }

    .items-list-container .mud-table-row:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02) !important;
    }
</style>

